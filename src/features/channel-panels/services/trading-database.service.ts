import { RedisDatabaseService } from '@/core/database';
import { Injectable, Logger } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';

// Redis Entity Types
export interface TradingPortfolio {
  id: string;
  userId: string;
  guildId: string;
  name: string;
  description?: string;
  totalValue: number;
  dailyChange: number;
  dailyChangePercent: number;
  isPublic: boolean;
  currency: string;
  riskLevel: string;
  strategy?: string;
  lastUpdated: string;
  createdAt: string;
  updatedAt: string;
}

export interface PortfolioHolding {
  id: string;
  portfolioId: string;
  symbol: string;
  quantity: number;
  averagePrice: number;
  currentPrice: number;
  marketValue: number;
  unrealizedPnL: number;
  unrealizedPnLPercent: number;
  purchaseDate: string;
  createdAt: string;
  updatedAt: string;
}

export interface TradingAlert {
  id: string;
  userId: string;
  guildId: string;
  symbol: string;
  condition: string;
  targetPrice: number;
  currentPrice?: number;
  message?: string;
  isActive: boolean;
  isTriggered: boolean;
  triggeredAt?: string;
  alertType: string;
  createdAt: string;
  updatedAt: string;
}

export interface TradingStrategy {
  id: string;
  name: string;
  description: string;
  userId: string;
  guildId: string;
  entryRules: string[];
  exitRules: string[];
  riskManagement: any;
  performance: any;
  isPublic: boolean;
  backtestResults?: any;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

export interface TradingTransaction {
  id: string;
  portfolioId: string;
  symbol: string;
  type: string; // 'buy', 'sell'
  quantity: number;
  price: number;
  totalValue: number;
  fees: number;
  notes?: string;
  executedAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface Watchlist {
  id: string;
  userId: string;
  guildId: string;
  name: string;
  symbols: string[];
  isDefault: boolean;
  isPublic: boolean;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

export interface MarketData {
  id: string;
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  high24h: number;
  low24h: number;
  marketCap?: number;
  lastUpdated: string;
  createdAt: string;
  updatedAt: string;
}

export interface PortfolioWithStats extends TradingPortfolio {
  holdingsCount?: number;
  transactionCount?: number;
  performance?: {
    dayChange: number;
    weekChange: number;
    monthChange: number;
  };
}

export interface TradingMetrics {
  totalPortfolios: number;
  totalPortfolioValue: number;
  activeTraders: number;
  totalTransactions: number;
  averageReturn: number;
  popularSymbols: Array<{ symbol: string; popularity: number }>;
  marketSentiment: {
    bullish: number;
    bearish: number;
    neutral: number;
  };
}

@Injectable()
export class TradingDatabaseService {
  private readonly logger = new Logger(TradingDatabaseService.name);

  constructor(private readonly databaseService: RedisDatabaseService) {}

  private get redis() {
    return this.databaseService.redis.getClient();
  }

  // Helper function to parse Redis hash data to MarketData
  private parseMarketData(data: Record<string, string>, symbol: string): MarketData {
    return {
      id: data.id || '',
      symbol: data.symbol || symbol,
      price: parseFloat(data.price || '0') || 0,
      change: parseFloat(data.change || '0') || 0,
      changePercent: parseFloat(data.changePercent || '0') || 0,
      volume: parseFloat(data.volume || '0') || 0,
      high24h: parseFloat(data.high24h || '0') || 0,
      low24h: parseFloat(data.low24h || '0') || 0,
      marketCap: data.marketCap ? parseFloat(data.marketCap) : undefined,
      lastUpdated: data.lastUpdated || new Date().toISOString(),
      createdAt: data.createdAt || new Date().toISOString(),
      updatedAt: data.updatedAt || new Date().toISOString()
    };
  }

  // Market Data Management
  async updateMarketData(symbol: string, data: Omit<MarketData, 'id' | 'symbol' | 'createdAt' | 'updatedAt'>): Promise<MarketData> {
    try {
      const key = `market_data:${symbol}`;
      const now = new Date().toISOString();

      // Get existing data
      const existing = await this.redis.hgetall(key);

      const marketDataEntry: MarketData = {
        id: existing.id || uuidv4(),
        symbol,
        ...data,
        lastUpdated: now,
        createdAt: existing.createdAt || now,
        updatedAt: now
      };

      // Store in Redis
      await this.redis.hset(key, marketDataEntry);

      // Add to symbol index
      await this.redis.sadd('market_data:symbols', symbol);

      this.logger.log(`Updated market data for ${symbol}`);
      return marketDataEntry;
    } catch (error) {
      this.logger.error(`Failed to update market data for ${symbol}:`, error);
      throw error;
    }
  }

  async getMarketData(symbols?: string[], market?: string, limit: number = 50): Promise<MarketData[]> {
    try {
      let marketDataList: MarketData[] = [];

      if (symbols && symbols.length > 0) {
        // Get specific symbols
        for (const symbol of symbols) {
          const key = `market_data:${symbol}`;
          const data = await this.redis.hgetall(key);
          if (data && Object.keys(data).length > 0) {
            // Parse Redis hash data to MarketData
            const marketData: MarketData = {
              id: data.id || '',
              symbol: data.symbol || symbol,
              price: parseFloat(data.price || '0') || 0,
              change: parseFloat(data.change || '0') || 0,
              changePercent: parseFloat(data.changePercent || '0') || 0,
              volume: parseFloat(data.volume || '0') || 0,
              high24h: parseFloat(data.high24h || '0') || 0,
              low24h: parseFloat(data.low24h || '0') || 0,
              marketCap: data.marketCap ? parseFloat(data.marketCap) : undefined,
              lastUpdated: data.lastUpdated || new Date().toISOString(),
              createdAt: data.createdAt || new Date().toISOString(),
              updatedAt: data.updatedAt || new Date().toISOString()
            };
            marketDataList.push(marketData);
          }
        }
      } else {
        // Get all symbols from index
        const allSymbols = await this.redis.smembers('market_data:symbols');

        for (const symbol of allSymbols.slice(0, limit)) {
          const key = `market_data:${symbol}`;
          const data = await this.redis.hgetall(key);
          if (data && Object.keys(data).length > 0) {
            // Parse Redis hash data to MarketData
            const marketData: MarketData = {
              id: data.id || '',
              symbol: data.symbol || symbol,
              price: parseFloat(data.price || '0') || 0,
              change: parseFloat(data.change || '0') || 0,
              changePercent: parseFloat(data.changePercent || '0') || 0,
              volume: parseFloat(data.volume || '0') || 0,
              high24h: parseFloat(data.high24h || '0') || 0,
              low24h: parseFloat(data.low24h || '0') || 0,
              marketCap: data.marketCap ? parseFloat(data.marketCap) : undefined,
              lastUpdated: data.lastUpdated || new Date().toISOString(),
              createdAt: data.createdAt || new Date().toISOString(),
              updatedAt: data.updatedAt || new Date().toISOString()
            };
            marketDataList.push(marketData);
          }
        }
      }

      // Filter by market if specified
      if (market) {
        marketDataList = marketDataList.filter(item => (item as any).market === market);
      }

      // Sort by volume (descending) and limit
      return marketDataList
        .sort((a, b) => (b.volume || 0) - (a.volume || 0))
        .slice(0, limit);
    } catch (error) {
      this.logger.error('Failed to get market data:', error);
      throw error;
    }
  }

  async searchSymbols(searchTerm: string, limit: number = 10): Promise<MarketData[]> {
    try {
      const allSymbols = await this.redis.smembers('market_data:symbols');
      const matchingData: MarketData[] = [];

      // Search through symbols
      for (const symbol of allSymbols) {
        if (symbol.toLowerCase().includes(searchTerm.toLowerCase())) {
          const key = `market_data:${symbol}`;
          const data = await this.redis.hgetall(key);

          if (data && Object.keys(data).length > 0) {
            const marketData: MarketData = {
              id: data.id || '',
              symbol: data.symbol || symbol,
              price: parseFloat(data.price || '0') || 0,
              change: parseFloat(data.change || '0') || 0,
              changePercent: parseFloat(data.changePercent || '0') || 0,
              volume: parseFloat(data.volume || '0') || 0,
              high24h: parseFloat(data.high24h || '0') || 0,
              low24h: parseFloat(data.low24h || '0') || 0,
              marketCap: data.marketCap ? parseFloat(data.marketCap) : undefined,
              lastUpdated: data.lastUpdated || new Date().toISOString(),
              createdAt: data.createdAt || new Date().toISOString(),
              updatedAt: data.updatedAt || new Date().toISOString()
            };
            matchingData.push(marketData);
          }
        }
      }

      // Sort by volume and limit
      return matchingData
        .sort((a, b) => (b.volume || 0) - (a.volume || 0))
        .slice(0, limit);
    } catch (error) {
      this.logger.error('Failed to search symbols:', error);
      throw error;
    }
  }

  // Portfolio Management
  async createPortfolio(portfolioData: Omit<TradingPortfolio, 'id' | 'createdAt' | 'updatedAt'>): Promise<TradingPortfolio> {
    try {
      const [portfolio] = await this.db.insert(tradingPortfolios)
        .values(portfolioData)
        .returning();
      
      this.logger.log(`Created trading portfolio: ${portfolio.name} (${portfolio.id})`);
      return portfolio;
    } catch (error) {
      this.logger.error('Failed to create portfolio:', error);
      throw error;
    }
  }

  async getUserPortfolios(userId: string, guildId: string): Promise<PortfolioWithStats[]> {
    try {
      const portfolios = await this.db
        .select({
          id: tradingPortfolios.id,
          userId: tradingPortfolios.userId,
          name: tradingPortfolios.name,
          description: tradingPortfolios.description,
          totalValue: tradingPortfolios.totalValue,
          totalCost: tradingPortfolios.totalCost,
          totalGainLoss: tradingPortfolios.totalGainLoss,
          totalGainLossPercent: tradingPortfolios.totalGainLossPercent,
          isDefault: tradingPortfolios.isDefault,
          isPublic: tradingPortfolios.isPublic,
          riskLevel: tradingPortfolios.riskLevel,
          strategy: tradingPortfolios.strategy,
          guildId: tradingPortfolios.guildId,
          lastUpdated: tradingPortfolios.lastUpdated,
          createdAt: tradingPortfolios.createdAt,
          updatedAt: tradingPortfolios.updatedAt,
          deletedAt: tradingPortfolios.deletedAt,
          holdingsCount: sql<number>`COUNT(DISTINCT ${portfolioHoldings.id})`,
          transactionCount: sql<number>`COUNT(DISTINCT ${tradingTransactions.id})`
        })
        .from(tradingPortfolios)
        .leftJoin(portfolioHoldings, eq(portfolioHoldings.portfolioId, tradingPortfolios.id))
        .leftJoin(tradingTransactions, eq(tradingTransactions.portfolioId, tradingPortfolios.id))
        .where(and(
          eq(tradingPortfolios.userId, userId),
          eq(tradingPortfolios.guildId, guildId)
        ))
        .groupBy(tradingPortfolios.id)
        .orderBy(desc(tradingPortfolios.isDefault), desc(tradingPortfolios.totalValue));

      return portfolios as PortfolioWithStats[];
    } catch (error) {
      this.logger.error('Failed to get user portfolios:', error);
      throw error;
    }
  }

  async updatePortfolioValue(portfolioId: number): Promise<void> {
    try {
      const holdings = await this.db
        .select()
        .from(portfolioHoldings)
        .where(eq(portfolioHoldings.portfolioId, portfolioId));

      let totalValue = 0;
      let totalCost = 0;

      for (const holding of holdings) {
        totalValue += parseFloat(holding.currentValue.toString());
        totalCost += parseFloat(holding.totalCost.toString());
      }

      const totalGainLoss = totalValue - totalCost;
      const totalGainLossPercent = totalCost > 0 ? (totalGainLoss / totalCost) * 100 : 0;

      await this.db
        .update(tradingPortfolios)
        .set({
          totalValue: totalValue.toString(),
          totalCost: totalCost.toString(),
          totalGainLoss: totalGainLoss.toString(),
          totalGainLossPercent: totalGainLossPercent.toString(),
          lastUpdated: new Date(),
          updatedAt: new Date()
        } as any)
        .where(eq(tradingPortfolios.id, portfolioId));

      this.logger.log(`Updated portfolio ${portfolioId} value: ${totalValue}`);
    } catch (error) {
      this.logger.error('Failed to update portfolio value:', error);
      throw error;
    }
  }

  // Holdings Management
  async addHolding(holdingData: Omit<NewPortfolioHolding, 'id' | 'createdAt' | 'updatedAt'>): Promise<PortfolioHolding> {
    try {
      // Check if holding already exists
      const existing = await this.db
        .select()
        .from(portfolioHoldings)
        .where(and(
          eq(portfolioHoldings.portfolioId, holdingData.portfolioId),
          eq(portfolioHoldings.symbol, holdingData.symbol)
        ))
        .limit(1);

      if (existing.length > 0) {
        // Update existing holding
        const currentQuantity = parseFloat(existing[0].quantity.toString());
        const currentCost = parseFloat(existing[0].totalCost.toString());
        const newQuantity = currentQuantity + parseFloat(holdingData.quantity.toString());
        const newCost = currentCost + parseFloat(holdingData.totalCost.toString());
        const newAverageCost = newQuantity > 0 ? newCost / newQuantity : 0;

        const [updated] = await this.db
          .update(portfolioHoldings)
          .set({
            quantity: newQuantity.toString(),
            averageCost: newAverageCost.toString(),
            totalCost: newCost.toString(),
            currentValue: (newQuantity * parseFloat(holdingData.currentPrice.toString())).toString(),
            gainLoss: ((newQuantity * parseFloat(holdingData.currentPrice.toString())) - newCost).toString(),
            gainLossPercent: newCost > 0 ? (((newQuantity * parseFloat(holdingData.currentPrice.toString())) - newCost) / newCost * 100).toString() : '0',
            lastUpdated: new Date(),
            updatedAt: new Date()
          } as any)
          .where(eq(portfolioHoldings.id, existing[0].id))
          .returning();

        return updated;
      }

      const [holding] = await this.db.insert(portfolioHoldings)
        .values(holdingData)
        .returning();
      
      // Update portfolio value
      await this.updatePortfolioValue(holdingData.portfolioId);
      
      this.logger.log(`Added holding ${holdingData.symbol} to portfolio ${holdingData.portfolioId}`);
      return holding;
    } catch (error) {
      this.logger.error('Failed to add holding:', error);
      throw error;
    }
  }

  async getPortfolioHoldings(portfolioId: number): Promise<PortfolioHolding[]> {
    try {
      return await this.db
        .select()
        .from(portfolioHoldings)
        .where(eq(portfolioHoldings.portfolioId, portfolioId))
        .orderBy(desc(portfolioHoldings.allocation));
    } catch (error) {
      this.logger.error('Failed to get portfolio holdings:', error);
      throw error;
    }
  }

  // Transaction Management
  async addTransaction(transactionData: Omit<NewTradingTransaction, 'id' | 'createdAt' | 'updatedAt'>): Promise<TradingTransaction> {
    try {
      const [transaction] = await this.db.insert(tradingTransactions)
        .values(transactionData)
        .returning();

      // Update holdings based on transaction
      if (transactionData.type === 'buy') {
        await this.addHolding({
          portfolioId: transactionData.portfolioId,
          symbol: transactionData.symbol,
          quantity: transactionData.quantity,
          averageCost: transactionData.price,
          currentPrice: transactionData.price,
          totalCost: transactionData.totalAmount,
          currentValue: transactionData.totalAmount,
          gainLoss: '0',
          gainLossPercent: '0',
          allocation: '0', // Will be calculated when portfolio is updated
          firstPurchaseDate: transactionData.transactionDate
        });
      } else if (transactionData.type === 'sell') {
        // Handle sell transaction - reduce holdings
        const existing = await this.db
          .select()
          .from(portfolioHoldings)
          .where(and(
            eq(portfolioHoldings.portfolioId, transactionData.portfolioId),
            eq(portfolioHoldings.symbol, transactionData.symbol)
          ))
          .limit(1);

        if (existing.length > 0) {
          const currentQuantity = parseFloat(existing[0].quantity.toString());
          const sellQuantity = parseFloat(transactionData.quantity.toString());
          const newQuantity = currentQuantity - sellQuantity;

          if (newQuantity <= 0) {
            // Remove holding completely
            await this.db
              .delete(portfolioHoldings)
              .where(eq(portfolioHoldings.id, existing[0].id));
          } else {
            // Update holding quantity
            const currentCost = parseFloat(existing[0].totalCost.toString());
            const sellProportion = sellQuantity / currentQuantity;
            const newCost = currentCost * (1 - sellProportion);

            await this.db
              .update(portfolioHoldings)
              .set({
                quantity: newQuantity.toString(),
                totalCost: newCost.toString(),
                currentValue: (newQuantity * parseFloat(existing[0].currentPrice.toString())).toString(),
                lastUpdated: new Date(),
                updatedAt: new Date()
              } as any)
              .where(eq(portfolioHoldings.id, existing[0].id));
          }
        }
      }

      // Update portfolio value
      await this.updatePortfolioValue(transactionData.portfolioId);
      
      this.logger.log(`Added ${transactionData.type} transaction for ${transactionData.symbol}`);
      return transaction;
    } catch (error) {
      this.logger.error('Failed to add transaction:', error);
      throw error;
    }
  }

  async getPortfolioTransactions(portfolioId: number, limit: number = 50): Promise<TradingTransaction[]> {
    try {
      return await this.db
        .select()
        .from(tradingTransactions)
        .where(eq(tradingTransactions.portfolioId, portfolioId))
        .orderBy(desc(tradingTransactions.transactionDate))
        .limit(limit);
    } catch (error) {
      this.logger.error('Failed to get portfolio transactions:', error);
      throw error;
    }
  }

  // Alert Management
  async createAlert(alertData: Omit<NewTradingAlert, 'id' | 'createdAt' | 'updatedAt'>): Promise<TradingAlert> {
    try {
      const [alert] = await this.db.insert(tradingAlerts)
        .values(alertData)
        .returning();
      
      this.logger.log(`Created trading alert for ${alertData.symbol}: ${alertData.alertType}`);
      return alert;
    } catch (error) {
      this.logger.error('Failed to create alert:', error);
      throw error;
    }
  }

  async getUserAlerts(userId: string, guildId: string, isActive?: boolean): Promise<TradingAlert[]> {
    try {
      let whereConditions = [
        eq(tradingAlerts.userId, userId),
        eq(tradingAlerts.guildId, guildId)
      ];
      
      if (isActive !== undefined) {
        whereConditions.push(eq(tradingAlerts.isActive, isActive));
      }

      return await this.db
        .select()
        .from(tradingAlerts)
        .where(and(...whereConditions))
        .orderBy(desc(tradingAlerts.createdAt));
    } catch (error) {
      this.logger.error('Failed to get user alerts:', error);
      throw error;
    }
  }

  async checkAlerts(): Promise<TradingAlert[]> {
    try {
      // Get all active alerts
      const alerts = await this.db
        .select()
        .from(tradingAlerts)
        .where(and(
          eq(tradingAlerts.isActive, true),
          eq(tradingAlerts.notificationSent, false)
        ));

      const triggeredAlerts: TradingAlert[] = [];

      for (const alert of alerts) {
        // Get current market data
        const [currentData] = await this.db
          .select()
          .from(marketData)
          .where(eq(marketData.symbol, alert.symbol))
          .limit(1);

        if (!currentData) continue;

        const currentPrice = parseFloat(currentData.currentPrice.toString());
        const targetValue = parseFloat(alert.targetValue.toString());
        let triggered = false;

        switch (alert.alertType) {
          case 'price_above':
            triggered = currentPrice >= targetValue;
            break;
          case 'price_below':
            triggered = currentPrice <= targetValue;
            break;
          case 'volume_spike':
            // TODO: Implement volume spike detection
            break;
        }

        if (triggered) {
          await this.db
            .update(tradingAlerts)
            .set({
              triggeredAt: new Date(),
              currentValue: currentPrice.toString(),
              updatedAt: new Date()
            } as any)
            .where(eq(tradingAlerts.id, alert.id));

          triggeredAlerts.push({
            ...alert,
            triggeredAt: new Date(),
            currentValue: currentPrice.toString()
          });
        }
      }

      return triggeredAlerts;
    } catch (error) {
      this.logger.error('Failed to check alerts:', error);
      throw error;
    }
  }

  // Strategy Management
  async createStrategy(strategyData: Omit<NewTradingStrategy, 'id' | 'createdAt' | 'updatedAt'>): Promise<TradingStrategy> {
    try {
      const [strategy] = await this.db.insert(tradingStrategies)
        .values(strategyData)
        .returning();
      
      this.logger.log(`Created trading strategy: ${strategy.name} (${strategy.id})`);
      return strategy;
    } catch (error) {
      this.logger.error('Failed to create strategy:', error);
      throw error;
    }
  }

  async getUserStrategies(userId: string, guildId: string): Promise<TradingStrategy[]> {
    try {
      return await this.db
        .select()
        .from(tradingStrategies)
        .where(and(
          eq(tradingStrategies.userId, userId),
          eq(tradingStrategies.guildId, guildId)
        ))
        .orderBy(desc(tradingStrategies.createdAt));
    } catch (error) {
      this.logger.error('Failed to get user strategies:', error);
      throw error;
    }
  }

  // Watchlist Management
  async createWatchlist(watchlistData: Omit<NewWatchlist, 'id' | 'createdAt' | 'updatedAt'>): Promise<Watchlist> {
    try {
      const [watchlist] = await this.db.insert(watchlists)
        .values(watchlistData)
        .returning();
      
      this.logger.log(`Created watchlist: ${watchlist.name} (${watchlist.id})`);
      return watchlist;
    } catch (error) {
      this.logger.error('Failed to create watchlist:', error);
      throw error;
    }
  }

  async getUserWatchlists(userId: string, guildId: string): Promise<Watchlist[]> {
    try {
      return await this.db
        .select()
        .from(watchlists)
        .where(and(
          eq(watchlists.userId, userId),
          eq(watchlists.guildId, guildId)
        ))
        .orderBy(desc(watchlists.createdAt));
    } catch (error) {
      this.logger.error('Failed to get user watchlists:', error);
      throw error;
    }
  }

  async addToWatchlist(watchlistId: number, symbol: string): Promise<{ success: boolean; message: string }> {
    try {
      const [watchlist] = await this.db
        .select()
        .from(watchlists)
        .where(eq(watchlists.id, watchlistId))
        .limit(1);

      if (!watchlist) {
        return { success: false, message: 'Watchlist not found!' };
      }

      const currentSymbols = watchlist.symbols || [];
      if (currentSymbols.includes(symbol)) {
        return { success: false, message: 'Symbol already in watchlist!' };
      }

      await this.db
        .update(watchlists)
        .set({
          symbols: [...currentSymbols, symbol],
          updatedAt: new Date()
        } as any)
        .where(eq(watchlists.id, watchlistId));

      return { success: true, message: 'Symbol added to watchlist!' };
    } catch (error) {
      this.logger.error('Failed to add to watchlist:', error);
      return { success: false, message: 'Failed to add symbol to watchlist. Please try again.' };
    }
  }

  // Analytics
  async getTradingMetrics(guildId: string): Promise<TradingMetrics> {
    try {
      const [portfolioStats] = await this.db
        .select({
          totalPortfolios: sql<number>`COUNT(*)`,
          totalPortfolioValue: sql<number>`COALESCE(SUM(CAST(total_value AS DECIMAL)), 0)`,
          activeTraders: sql<number>`COUNT(DISTINCT user_id)`
        })
        .from(tradingPortfolios)
        .where(eq(tradingPortfolios.guildId, guildId));

      const [transactionStats] = await this.db
        .select({
          totalTransactions: sql<number>`COUNT(*)`
        })
        .from(tradingTransactions)
        .innerJoin(tradingPortfolios, eq(tradingTransactions.portfolioId, tradingPortfolios.id))
        .where(eq(tradingPortfolios.guildId, guildId));

      const popularSymbols = await this.db
        .select({
          symbol: tradingTransactions.symbol,
          popularity: sql<number>`COUNT(*)`
        })
        .from(tradingTransactions)
        .innerJoin(tradingPortfolios, eq(tradingTransactions.portfolioId, tradingPortfolios.id))
        .where(eq(tradingPortfolios.guildId, guildId))
        .groupBy(tradingTransactions.symbol)
        .orderBy(desc(sql`COUNT(*)`))
        .limit(10);

      return {
        totalPortfolios: portfolioStats.totalPortfolios || 0,
        totalPortfolioValue: portfolioStats.totalPortfolioValue || 0,
        activeTraders: portfolioStats.activeTraders || 0,
        totalTransactions: transactionStats.totalTransactions || 0,
        averageReturn: 0, // TODO: Calculate average return
        popularSymbols: popularSymbols.map((s: any) => ({ symbol: s.symbol, popularity: s.popularity })),
        marketSentiment: {
          bullish: 0,
          bearish: 0,
          neutral: 0
        } // TODO: Calculate market sentiment
      };
    } catch (error) {
      this.logger.error('Failed to get trading metrics:', error);
      throw error;
    }
  }

  // User Management
  async ensureUser(discordId: string, username: string): Promise<void> {
    try {
      const [existingUser] = await this.db
        .select()
        .from(users)
        .where(eq(users.discordId, discordId))
        .limit(1);

      if (!existingUser) {
        await this.db.insert(users).values({
          discordId,
          username,
          isActive: true,
          lastActivityAt: new Date(),
          preferences: {},
          profile: {}
        } as any);
        
        this.logger.log(`Created new user: ${username} (${discordId})`);
      } else if (existingUser.username !== username) {
        await this.db
          .update(users)
          .set({ 
            username,
            lastActivityAt: new Date()
          } as any)
          .where(eq(users.discordId, discordId));
      }
    } catch (error) {
      this.logger.error('Failed to ensure user:', error);
      throw error;
    }
  }
}