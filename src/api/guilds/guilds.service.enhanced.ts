import { GuildFeatures } from '@/core/database/schema';
import { BadRequestException, ForbiddenException, Injectable, Logger, NotFoundException } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import {
    Guild,
    CreateGuild as GuildCreateType,
    GuildSettings
} from '@/core/database/entities/guild.entity';
import type {
    PermissionsString,
    Snowflake
} from 'discord.js';
import { CacheService } from '../../core/cache/cache.service';
import { DatabaseService } from '../../core/database/database.service';
import { DiscordService } from '../../discord/discord.service';
import { DiscordUtilsService } from '../../discord/utils/discord-utils.service';

// ====== DISCORD TYPES ======

/**
 * Discord guild information with typed features
 */
export interface DiscordGuildInfo {
  id: Snowflake;
  name: string;
  icon: string | null;
  iconURL?: string | null;
  memberCount: number;
  owner: Snowflake | null;
  ownerId?: Snowflake;
  features: string[];
  description?: string | null;
  preferredLocale?: string;
  premiumTier?: number;
  premiumSubscriptionCount?: number;
  verificationLevel?: number;
  explicitContentFilter?: number;
  defaultMessageNotifications?: number;
  afkChannelId?: Snowflake | null;
  afkTimeout?: number;
  systemChannelId?: Snowflake | null;
  rulesChannelId?: Snowflake | null;
  publicUpdatesChannelId?: Snowflake | null;
  maxMembers?: number;
  maxPresences?: number;
}

/**
 * Discord channel information with enhanced typing
 */
export interface DiscordChannelInfo {
  id: Snowflake;
  name: string;
  type: number;
  position?: number;
  parentId?: Snowflake | null;
  permissionOverwrites?: PermissionOverwrite[];
  topic?: string | null;
  nsfw?: boolean;
  lastMessageId?: Snowflake | null;
  bitrate?: number;
  userLimit?: number;
  rateLimitPerUser?: number;
  rtcRegion?: string | null;
  videoQualityMode?: number;
}

export interface PermissionOverwrite {
  id: Snowflake;
  type: 0 | 1; // 0 for role, 1 for member
  allow: string;
  deny: string;
}

/**
 * Discord role information with permissions
 */
export interface DiscordRoleInfo {
  id: Snowflake;
  name: string;
  color: number;
  hexColor?: string;
  hoist: boolean;
  icon?: string | null;
  unicodeEmoji?: string | null;
  position: number;
  permissions: string;
  managed: boolean;
  mentionable: boolean;
  flags?: number;
}

/**
 * Discord member information with roles and permissions
 */
export interface DiscordMemberInfo {
  user: {
    id: Snowflake;
    username: string;
    discriminator: string;
    globalName?: string | null;
    avatar?: string | null;
    bot?: boolean;
    system?: boolean;
    mfaEnabled?: boolean;
    banner?: string | null;
    accentColor?: number | null;
    locale?: string;
    verified?: boolean;
    email?: string | null;
    flags?: number;
    premiumType?: number;
    publicFlags?: number;
  };
  nick?: string | null;
  avatar?: string | null;
  roles: Snowflake[];
  joinedAt: string;
  premiumSince?: string | null;
  deaf: boolean;
  mute: boolean;
  flags: number;
  pending?: boolean;
  permissions?: string;
  communicationDisabledUntil?: string | null;
}

// ====== SERVICE TYPES ======

/**
 * API request/response types
 */
export interface GuildInfoResponse {
  guild: DiscordGuildInfo & {
    features: string[];
    settings: GuildSettings;
    dbFeatures: GuildFeatures;
    lastActivity?: Date;
  };
  permissions: {
    manage: boolean;
    administrator: boolean;
    manageChannels: boolean;
    manageRoles: boolean;
  };
}

export interface GuildChannelsResponse {
  channels: DiscordChannelInfo[];
  categories: DiscordChannelInfo[];
  textChannels: DiscordChannelInfo[];
  voiceChannels: DiscordChannelInfo[];
  totalChannels: number;
}

export interface GuildRolesResponse {
  roles: DiscordRoleInfo[];
  totalRoles: number;
  managedRoles: DiscordRoleInfo[];
  assignableRoles: DiscordRoleInfo[];
}

export interface GuildMembersResponse {
  members?: DiscordMemberInfo[];
  totalMembers: number;
  onlineMembers?: number;
  bots?: number;
  hasPermission: boolean;
}

export interface GuildStatsResponse {
  guildId: string;
  memberCount: number;
  channelCount: number;
  roleCount: number;
  features: string[];
  lastActivity?: Date;
  isActive: boolean;
  premiumTier?: number;
  boostCount?: number;
}

/**
 * Feature configuration types
 */
export interface FeatureConfigResponse<T = any> {
  guildId: string;
  feature: string;
  config: T;
  enabled: boolean;
  lastModified?: Date;
  modifiedBy?: string;
}

export interface FeatureUpdateRequest<T = any> {
  config: T;
  enabled?: boolean;
}

export interface FeatureUpdateResponse<T = any> {
  guildId: string;
  feature: string;
  config: T;
  previousConfig?: T;
  message: string;
  timestamp: Date;
}

/**
 * AI Models configuration types
 */
export interface AIModelInfo {
  id: string;
  provider: 'anthropic' | 'openai';
  name: string;
  inputPrice: number;
  outputPrice: number;
  maxInputTokens: number;
  maxOutputTokens: number;
  contextWindow: number;
  costEstimation: {
    inputCostPer1K: string;
    outputCostPer1K: string;
    estimatedCostFor1000Messages: string;
  };
}

export interface AIModelsResponse {
  models: AIModelInfo[];
  defaultModel: string;
  providers: {
    anthropic: {
      name: string;
      models: AIModelInfo[];
    };
    openai: {
      name: string;
      models: AIModelInfo[];
    };
  };
}

/**
 * Permission and authentication types
 */
export interface AuthenticatedUser {
  id: string;
  userId?: string;
  sessionId?: string;
  discordId?: string;
  permissions?: PermissionsString[];
}

export interface GuildPermissionCheck {
  guildId: string;
  userId: string;
  hasAccess: boolean;
  permissions: {
    administrator: boolean;
    manageGuild: boolean;
    manageChannels: boolean;
    manageRoles: boolean;
    manageMessages: boolean;
    manageWebhooks: boolean;
    viewAuditLog: boolean;
  };
}

// ====== VALIDATION TYPES ======

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

export interface ConfigValidationResult<T = any> extends ValidationResult {
  config?: T;
  sanitizedConfig?: T;
}

// ====== ERROR TYPES ======

export class GuildServiceError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly guildId?: string,
    public readonly details?: any
  ) {
    super(message);
    this.name = 'GuildServiceError';
  }
}

export class GuildPermissionError extends GuildServiceError {
  constructor(guildId: string, userId: string, requiredPermission: string) {
    super(
      `User ${userId} lacks ${requiredPermission} permission in guild ${guildId}`,
      'INSUFFICIENT_PERMISSIONS',
      guildId,
      { userId, requiredPermission }
    );
  }
}

export class GuildConfigurationError extends GuildServiceError {
  constructor(guildId: string, feature: string, validationErrors: string[]) {
    super(
      `Invalid configuration for feature ${feature}: ${validationErrors.join(', ')}`,
      'INVALID_CONFIGURATION',
      guildId,
      { feature, validationErrors }
    );
  }
}

// ====== UTILITY TYPES ======

export type GuildFeatureKey = keyof GuildFeatures;

export type SupportedFeature = 
  | 'welcome-message' 
  | 'music' 
  | 'gaming' 
  | 'reaction-role' 
  | 'meme' 
  | 'user-command' 
  | 'leveling' 
  | 'moderation' 
  | 'economy' 
  | 'utility' 
  | 'starboard' 
  | 'whop' 
  | 'ai-agents' 
  | 'role-based-access' 
  | 'dev-on-demand' 
  | 'content-organization';

export interface FeatureStatus {
  feature: SupportedFeature;
  enabled: boolean;
  configured: boolean;
  lastModified?: Date;
}

export interface GuildOverview {
  guild: DiscordGuildInfo;
  features: FeatureStatus[];
  stats: GuildStatsResponse;
  health: {
    botConnected: boolean;
    permissionsValid: boolean;
    configurationValid: boolean;
    lastHealthCheck: Date;
  };
}

@Injectable()
export class GuildsService {
  private readonly logger = new Logger(GuildsService.name);

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly redisDatabaseService: CacheService,
    private readonly discordService: DiscordService,
    private readonly discordUtils: DiscordUtilsService
  ) {}

  // ====== TYPE-SAFE CONFIGURATION METHODS ======

  /**
   * Get default configuration for a specific feature with full type safety
   */
  private getTypedFeatureConfig<K extends SupportedFeature>(
    feature: K
  ): GuildFeatures[K] {
    return this.getDefaultFeatureConfig(feature) as GuildFeatures[K];
  }

  /**
   * Validate feature configuration with type safety
   */
  private validateFeatureConfig<K extends SupportedFeature>(
    feature: K,
    config: any
  ): ConfigValidationResult<GuildFeatures[K]> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Feature-specific validation
    switch (feature) {
      case 'ai-agents':
        return this.validateAIAgentsConfigTyped(config);
      case 'music':
        return this.validateMusicConfig(config);
      case 'moderation':
        return this.validateModerationConfig(config);
      case 'welcome-message':
        return this.validateWelcomeConfig(config);
      default:
        return this.validateGenericFeatureConfig(config);
    }
  }

  /**
   * Validate AI agents configuration with enhanced type safety
   */
  private validateAIAgentsConfigTyped(config: any): ConfigValidationResult {
    const validation = this.validateAIAgentsConfig(config);
    return {
      isValid: validation.isValid,
      errors: validation.errors,
      config: validation.isValid ? config : undefined,
      sanitizedConfig: this.sanitizeAIAgentsConfig(config)
    };
  }

  /**
   * Validate music configuration
   */
  private validateMusicConfig(config: any): ConfigValidationResult {
    const errors: string[] = [];
    
    if (config.volume !== undefined && (config.volume < 0 || config.volume > 100)) {
      errors.push('Volume must be between 0 and 100');
    }
    
    if (config.maxQueueSize !== undefined && config.maxQueueSize < 1) {
      errors.push('Max queue size must be at least 1');
    }

    if (config.autoLeaveTime !== undefined && config.autoLeaveTime < 60) {
      errors.push('Auto leave time must be at least 60 seconds');
    }

    return {
      isValid: errors.length === 0,
      errors,
      config: errors.length === 0 ? config : undefined
    };
  }

  /**
   * Validate moderation configuration
   */
  private validateModerationConfig(config: any): ConfigValidationResult {
    const errors: string[] = [];
    
    if (config.warningThreshold !== undefined && config.warningThreshold < 1) {
      errors.push('Warning threshold must be at least 1');
    }
    
    if (config.autoKickThreshold !== undefined && config.autoKickThreshold < 1) {
      errors.push('Auto kick threshold must be at least 1');
    }

    if (config.autoBanThreshold !== undefined && config.autoBanThreshold < 1) {
      errors.push('Auto ban threshold must be at least 1');
    }

    return {
      isValid: errors.length === 0,
      errors,
      config: errors.length === 0 ? config : undefined
    };
  }

  /**
   * Validate welcome configuration
   */
  private validateWelcomeConfig(config: any): ConfigValidationResult {
    const errors: string[] = [];
    
    if (config.message && config.message.length > 2000) {
      errors.push('Welcome message must be under 2000 characters');
    }
    
    if (config.embedColor && !/^#[0-9A-Fa-f]{6}$/.test(config.embedColor)) {
      errors.push('Embed color must be a valid hex color code');
    }

    return {
      isValid: errors.length === 0,
      errors,
      config: errors.length === 0 ? config : undefined
    };
  }

  /**
   * Generic feature configuration validation
   */
  private validateGenericFeatureConfig(config: any): ConfigValidationResult {
    return {
      isValid: true,
      errors: [],
      config
    };
  }

  /**
   * Sanitize AI agents configuration
   */
  private sanitizeAIAgentsConfig(config: any): any {
    const availableModels = this.getAvailableModels();
    
    return {
      ...config,
      model: availableModels[config.model] ? config.model : 'claude-3-5-haiku-20241022',
      temperature: Math.max(0, Math.min(2, config.temperature || 0.7)),
      maxTokens: Math.max(1, config.maxTokens || 1500),
      rateLimitPerUser: Math.max(1, config.rateLimitPerUser || 10),
      rateLimitWindow: Math.max(60, config.rateLimitWindow || 3600)
    };
  }

  private getDefaultFeatureConfig(feature: string): any {
    const defaultConfigs: Record<string, any> = {
      'music': {
        enabled: false,
        volume: 50,
        maxQueueSize: 100,
        allowedChannels: [],
        allowedRoles: [],
        djRole: null,
        autoLeave: true,
        autoLeaveTime: 300
      },
      'welcome-message': {
        enabled: false,
        channel: null,
        message: 'Welcome to the server, {user}!',
        embedEnabled: false,
        embedColor: '#00ff00',
        dmEnabled: false,
        dmMessage: null
      },
      'gaming': {
        enabled: false,
        allowedChannels: [],
        availableGames: ['8ball', 'trivia', 'wordguess', 'slots'],
        economyIntegration: false
      },
      'reaction-role': {
        enabled: false,
        roles: []
      },
      'meme': {
        enabled: false,
        allowedChannels: [],
        autoPost: false,
        autoPostInterval: 3600,
        sources: ['reddit'],
        nsfw: false
      },
      'user-command': {
        enabled: false,
        prefix: '!',
        allowedChannels: [],
        showMemberCount: true,
        showRoleCount: true,
        showOnlineCount: true,
        showChannelCount: true,
        showBoostInfo: true,
        showServerAge: true,
        showBotCount: true,
        useEmbedFormat: true,
        embedColor: '#00ff00',
        customFooter: null,
        showThumbnail: true,
        commandName: 'serverinfo',
        allowedRoles: '',
        restrictToChannels: '',
        autoPostEnabled: false,
        autoPostChannel: null,
        autoPostInterval: 86400
      },
      'leveling': {
        enabled: false,
        xpPerMessage: 10,
        levelUpChannelId: null,
        roleRewards: [],
        messageCooldown: 60,
        xpMultiplier: 1.0,
        announceLevelUps: true,
        stackRoles: false
      },
      'moderation': {
        enabled: false,
        autoMod: false,
        logChannelId: null,
        mutedRoleId: null,
        warningThreshold: 3,
        autoKickThreshold: 5,
        autoBanThreshold: 7,
        deleteWarningsAfter: 2592000,
        spamDetection: false,
        linkDetection: false,
        capsDetection: false
      },
      'economy': {
        enabled: false,
        currency: 'coins',
        dailyReward: 100,
        workCooldown: 3600,
        startingBalance: 1000,
        dailyCooldown: 86400,
        crimeCooldown: 7200,
        robCooldown: 14400,
        shopEnabled: true,
        gamblingEnabled: true
      },
      'utility': {
        enabled: false,
        allowedChannels: [],
        features: ['weather', 'translate', 'qr', 'color'],
        apiKeys: {}
      },
      'starboard': {
        enabled: false,
        channelId: null,
        threshold: 3,
        emoji: '⭐',
        selfStar: false,
        botStar: false,
        excludeChannels: [],
        requireImage: false
      },
      'whop': {
        enabled: false,
        companyId: null,
        accessPassId: null,
        roleMapping: {},
        autoSync: true,
        syncInterval: 3600
      },
      'role-based-access': {
        enabled: false,
        tiers: [],
        premiumRoles: [],
        restrictions: {},
        autoAssign: false
      },
      'ai-agents': {
        enabled: false,
        provider: 'anthropic',
        model: 'claude-3-5-haiku-20241022',
        temperature: 0.7,
        maxTokens: 1500,
        systemPrompt: 'You are a helpful Discord bot assistant.',
        allowedChannels: [],
        allowedRoles: [],
        rateLimitPerUser: 10,
        rateLimitWindow: 3600,
        moderationEnabled: true,
        // Available models with pricing and capabilities
        availableModels: {
          // OpenAI Models
          'gpt-4': {
            provider: 'openai',
            name: 'GPT-4',
            inputPrice: 0.03,
            outputPrice: 0.06,
            maxInputTokens: 8192,
            maxOutputTokens: 4096,
            contextWindow: 8192
          },
          'gpt-3.5-turbo': {
            provider: 'openai',
            name: 'GPT-3.5 Turbo',
            inputPrice: 0.0015,
            outputPrice: 0.002,
            maxInputTokens: 4096,
            maxOutputTokens: 4096,
            contextWindow: 4096
          },
          // Claude 3 Models
          'claude-3-haiku-20240307': {
            provider: 'anthropic',
            name: 'Claude 3 Haiku',
            inputPrice: 0.25,
            outputPrice: 1.25,
            maxInputTokens: 200000,
            maxOutputTokens: 4096,
            contextWindow: 200000
          },
          'claude-3-sonnet-20240229': {
            provider: 'anthropic',
            name: 'Claude 3 Sonnet',
            inputPrice: 3.00,
            outputPrice: 15.00,
            maxInputTokens: 200000,
            maxOutputTokens: 4096,
            contextWindow: 200000
          },
          'claude-3-opus-20240229': {
            provider: 'anthropic',
            name: 'Claude 3 Opus',
            inputPrice: 15.00,
            outputPrice: 75.00,
            maxInputTokens: 200000,
            maxOutputTokens: 4096,
            contextWindow: 200000
          },
          // Claude 3.5 Models
          'claude-3-5-haiku-20241022': {
            provider: 'anthropic',
            name: 'Claude 3.5 Haiku',
            inputPrice: 0.80,
            outputPrice: 4.00,
            maxInputTokens: 200000,
            maxOutputTokens: 8192,
            contextWindow: 200000
          },
          'claude-3-5-sonnet-20240620': {
            provider: 'anthropic',
            name: 'Claude 3.5 Sonnet',
            inputPrice: 3.00,
            outputPrice: 15.00,
            maxInputTokens: 200000,
            maxOutputTokens: 8192,
            contextWindow: 200000
          },
          'claude-3-5-sonnet-20241022': {
            provider: 'anthropic',
            name: 'Claude 3.5 Sonnet v2',
            inputPrice: 3.00,
            outputPrice: 15.00,
            maxInputTokens: 200000,
            maxOutputTokens: 8192,
            contextWindow: 200000
          },
          // Claude 3.7 Models
          'claude-3-7-sonnet-20250219': {
            provider: 'anthropic',
            name: 'Claude 3.7 Sonnet',
            inputPrice: 3.00,
            outputPrice: 15.00,
            maxInputTokens: 200000,
            maxOutputTokens: 64000,
            contextWindow: 200000
          },
          // Claude 4 Models
          'claude-opus-4-20250514': {
            provider: 'anthropic',
            name: 'Claude 4 Opus',
            inputPrice: 15.00,
            outputPrice: 75.00,
            maxInputTokens: 200000,
            maxOutputTokens: 32000,
            contextWindow: 200000
          },
          'claude-sonnet-4-20250514': {
            provider: 'anthropic',
            name: 'Claude 4 Sonnet',
            inputPrice: 3.00,
            outputPrice: 15.00,
            maxInputTokens: 200000,
            maxOutputTokens: 64000,
            contextWindow: 200000
          }
        }
      },
      'dev-on-demand': {
        enabled: false,
        projectCategories: ['web', 'mobile', 'backend', 'frontend', 'fullstack', 'other'],
        skillLevels: ['beginner', 'intermediate', 'advanced', 'expert'],
        maxActiveProjects: 5,
        autoMatchmaking: false,
        requireApproval: true,
        developerRole: null,
        clientRole: null
      },
      'content-organization': {
        enabled: false,
        autoCreateCategories: false,
        categoryNamingScheme: 'default',
        channelNamingRules: {},
        archiveInactive: false,
        archiveAfterDays: 30,
        maxChannelsPerCategory: 50
      }
    };

    return defaultConfigs[feature] || { enabled: false };
  }

  private getAvailableModels(): Record<string, any> {
    const aiAgentsConfig = this.getDefaultFeatureConfig('ai-agents');
    return aiAgentsConfig.availableModels || {};
  }

  private validateAIAgentsConfig(config: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const availableModels = this.getAvailableModels();

    // Validate model selection
    if (config.model && !availableModels[config.model]) {
      const availableModelNames = Object.keys(availableModels).join(', ');
      errors.push(`Invalid model '${config.model}'. Available models: ${availableModelNames}`);
    }

    // Validate provider matches model
    if (config.model && config.provider && availableModels[config.model]) {
      const modelInfo = availableModels[config.model];
      if (modelInfo.provider !== config.provider) {
        errors.push(`Provider '${config.provider}' does not match model '${config.model}' which requires provider '${modelInfo.provider}'`);
      }
    }

    // Validate token limits
    if (config.model && config.maxTokens && availableModels[config.model]) {
      const modelInfo = availableModels[config.model];
      if (config.maxTokens > modelInfo.maxOutputTokens) {
        errors.push(`Max tokens ${config.maxTokens} exceeds model limit of ${modelInfo.maxOutputTokens} for ${config.model}`);
      }
    }

    // Validate temperature range
    if (config.temperature !== undefined) {
      if (config.temperature < 0 || config.temperature > 2) {
        errors.push('Temperature must be between 0 and 2');
      }
    }

    // Validate rate limiting
    if (config.rateLimitPerUser !== undefined && config.rateLimitPerUser < 1) {
      errors.push('Rate limit per user must be at least 1');
    }

    if (config.rateLimitWindow !== undefined && config.rateLimitWindow < 60) {
      errors.push('Rate limit window must be at least 60 seconds');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  async getAvailableAIModels(
    guildId: string, 
    user: AuthenticatedUser
  ): Promise<AIModelsResponse> {
    try {
      await this.checkGuildPermissions(guildId, user);
      
      const availableModels = this.getAvailableModels();
      
      // Add cost estimation helpers
      const modelsWithEstimation = Object.entries(availableModels).map(([modelId, model]) => ({
        id: modelId,
        ...model,
        costEstimation: {
          inputCostPer1K: (model.inputPrice / 1000).toFixed(4),
          outputCostPer1K: (model.outputPrice / 1000).toFixed(4),
          estimatedCostFor1000Messages: this.estimateUsageCost(model, 1000)
        }
      }));

      return {
        models: modelsWithEstimation,
        defaultModel: 'claude-3-5-haiku-20241022',
        providers: {
          anthropic: {
            name: 'Anthropic',
            models: modelsWithEstimation.filter((m: any) => m.provider === 'anthropic')
          },
          openai: {
            name: 'OpenAI',
            models: modelsWithEstimation.filter((m: any) => m.provider === 'openai')
          }
        }
      };
    } catch (error) {
      this.logger.error(`Failed to get available AI models for guild ${guildId}:`, error);
      throw error;
    }
  }

  private estimateUsageCost(model: any, messageCount: number): string {
    // Estimate average message: 100 input tokens, 50 output tokens
    const avgInputTokens = 100;
    const avgOutputTokens = 50;
    
    const inputCost = (messageCount * avgInputTokens * model.inputPrice) / 1000000;
    const outputCost = (messageCount * avgOutputTokens * model.outputPrice) / 1000000;
    
    return `$${(inputCost + outputCost).toFixed(4)}`;
  }

  async getGuildInfo(
    guildId: string, 
    user: AuthenticatedUser
  ): Promise<GuildInfoResponse> {
    if (!guildId || typeof guildId !== 'string') {
      throw new BadRequestException('Invalid guild ID provided');
    }

    try {
      // Check permissions with enhanced validation
      const permissionCheck = await this.checkEnhancedGuildPermissions(guildId, user);

      // Get guild from Discord with proper typing
      const discordGuild = await this.discordService.getGuildInfo(guildId) as DiscordGuildInfo;
      if (!discordGuild) {
        throw new NotFoundException(`Guild ${guildId} not found or bot not in guild`);
      }
      
      // Get guild from Redis with type safety
      const dbGuild = await this.getTypedGuildFromRedis(guildId);
      
      const guildInfo: GuildInfoResponse = {
        guild: {
          ...discordGuild,
          features: Array.isArray(discordGuild.features) ? discordGuild.features : [],
          settings: dbGuild?.config || {} as GuildSettings,
          dbFeatures: dbGuild?.features || {} as GuildFeatures,
          lastActivity: dbGuild?.lastActivityAt
        },
        permissions: {
          manage: permissionCheck.permissions.manageGuild,
          administrator: permissionCheck.permissions.administrator,
          manageChannels: permissionCheck.permissions.manageChannels,
          manageRoles: permissionCheck.permissions.manageRoles
        }
      };
      
      return guildInfo;
    } catch (error) {
      this.logger.error(`Failed to get guild info for ${guildId}:`, error);
      throw error;
    }
  }

  async getEnabledFeatures(
    guildId: string, 
    user: AuthenticatedUser
  ): Promise<{ guildId: string; features: SupportedFeature[] }> {
    try {
      await this.checkGuildPermissions(guildId, user);
      
      const features = await this.discordService.getEnabledFeatures(guildId);
      return { guildId, features: Array.isArray(features) ? features as SupportedFeature[] : [] };
    } catch (error) {
      this.logger.error(`Failed to get features for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getFeatureConfig<K extends SupportedFeature>(
    guildId: string, 
    feature: K, 
    user: AuthenticatedUser
  ): Promise<FeatureConfigResponse<GuildFeatures[K]>> {
    try {
      await this.checkGuildPermissions(guildId, user);
      
      // Get guild from Redis with type safety
      const guild = await this.getTypedGuildFromRedis(guildId);
      
      // Get existing config or default config with type safety
      const config = guild?.features?.[feature] || this.getTypedFeatureConfig(feature);
      
      return {
        guildId,
        feature,
        config: config as GuildFeatures[K],
        enabled: config?.enabled || false,
        lastModified: guild?.updatedAt,
        modifiedBy: 'system'
      };
    } catch (error) {
      this.logger.error(`Failed to get feature ${feature} for guild ${guildId}:`, error);
      throw error;
    }
  }

  async updateFeature<K extends SupportedFeature>(
    guildId: string, 
    feature: K, 
    updateRequest: FeatureUpdateRequest<GuildFeatures[K]>, 
    user: AuthenticatedUser
  ): Promise<FeatureUpdateResponse<GuildFeatures[K]>> {
    if (!guildId || !feature || !updateRequest) {
      throw new BadRequestException('Missing required parameters');
    }

    try {
      // Enhanced permission checking
      await this.checkEnhancedGuildPermissions(guildId, user);

      // Type-safe validation
      const validation = this.validateFeatureConfig(feature, updateRequest.config);
      if (!validation.isValid) {
        throw new GuildConfigurationError(guildId, feature, validation.errors);
      }

      const sanitizedConfig = validation.sanitizedConfig || validation.config;
      const previousGuild = await this.getTypedGuildFromRedis(guildId);
      const previousConfig = previousGuild?.features?.[feature];

      // Update guild configuration in Redis with type safety
      let guild = await this.getTypedGuildFromRedis(guildId);
      if (!guild) {
        // Create guild entry if it doesn't exist
        const discordGuild = await this.discordService.getGuildInfo(guildId) as DiscordGuildInfo;
        const newGuild: GuildCreateType = {
          id: `guild_${Date.now()}`,
          discordId: guildId,
          name: discordGuild?.name || 'Unknown Guild',
          features: { [feature]: sanitizedConfig } as GuildFeatures,
          isActive: true,
          welcomeEnabled: false,
          starboardEnabled: false,
          starboardThreshold: 3,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        await this.saveGuildToRedis(guildId, newGuild);
        guild = newGuild;
      } else {
        const updatedFeatures: GuildFeatures = {
          ...guild.features,
          [feature]: {
            ...sanitizedConfig,
            enabled: updateRequest.enabled !== undefined ? updateRequest.enabled : sanitizedConfig?.enabled
          }
        };
        const updatedGuild = { ...guild, features: updatedFeatures, updatedAt: new Date() };
        await this.updateGuildInRedis(guildId, updatedGuild);
        guild = updatedGuild;
      }

      return {
        guildId,
        feature,
        config: guild.features[feature] as GuildFeatures[K],
        previousConfig: previousConfig as GuildFeatures[K] | undefined,
        message: 'Feature updated successfully',
        timestamp: new Date()
      };
    } catch (error) {
      this.logger.error(`Failed to update feature ${feature} for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getChannels(
    guildId: string, 
    user: AuthenticatedUser
  ): Promise<GuildChannelsResponse> {
    try {
      await this.checkEnhancedGuildPermissions(guildId, user);
      const channels = await this.discordUtils.getGuildChannels(guildId) as DiscordChannelInfo[];
      
      // Categorize channels by type
      const categories = channels.filter(c => c.type === 4); // CategoryChannel
      const textChannels = channels.filter(c => c.type === 0); // TextChannel
      const voiceChannels = channels.filter(c => c.type === 2); // VoiceChannel
      
      return {
        channels,
        categories,
        textChannels,
        voiceChannels,
        totalChannels: channels.length
      };
    } catch (error) {
      this.logger.error(`Failed to get channels for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getRoles(
    guildId: string, 
    user: AuthenticatedUser
  ): Promise<GuildRolesResponse> {
    try {
      await this.checkEnhancedGuildPermissions(guildId, user);
      const roles = await this.discordUtils.getGuildRoles(guildId) as DiscordRoleInfo[];
      
      // Categorize roles
      const managedRoles = roles.filter(r => r.managed);
      const assignableRoles = roles.filter(r => !r.managed && r.name !== '@everyone');
      
      return {
        roles,
        totalRoles: roles.length,
        managedRoles,
        assignableRoles
      };
    } catch (error) {
      this.logger.error(`Failed to get roles for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getMembers(
    guildId: string, 
    user: AuthenticatedUser
  ): Promise<GuildMembersResponse> {
    try {
      const permissionCheck = await this.checkEnhancedGuildPermissions(guildId, user);
      const memberCount = await this.getMemberCount(guildId);
      
      // Only return full member list if user has manage members permission
      const hasManageMembers = permissionCheck.permissions.administrator || 
        permissionCheck.permissions.manageRoles;
      
      return {
        totalMembers: memberCount,
        hasPermission: hasManageMembers,
        ...(hasManageMembers ? {
          // Would implement full member fetching here if permissions allow
          onlineMembers: 0,
          bots: 0
        } : {})
      };
    } catch (error) {
      this.logger.error(`Failed to get members for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getStats(
    guildId: string, 
    user: AuthenticatedUser
  ): Promise<GuildStatsResponse> {
    try {
      await this.checkEnhancedGuildPermissions(guildId, user);

      const guild = await this.getTypedGuildFromRedis(guildId);
      const discordGuild = await this.discordService.getGuildInfo(guildId) as DiscordGuildInfo;
      const channels = await this.discordUtils.getGuildChannels(guildId) as DiscordChannelInfo[];
      const roles = await this.discordUtils.getGuildRoles(guildId) as DiscordRoleInfo[];
      
      return {
        guildId,
        memberCount: discordGuild?.memberCount || 0,
        channelCount: channels?.length || 0,
        roleCount: roles?.length || 0,
        features: Object.keys(guild?.features || {}),
        lastActivity: guild?.lastActivityAt,
        isActive: guild?.isActive || false,
        premiumTier: discordGuild?.premiumTier,
        boostCount: discordGuild?.premiumSubscriptionCount
      };
    } catch (error) {
      this.logger.error(`Failed to get stats for guild ${guildId}:`, error);
      throw error;
    }
  }

  /**
   * Enhanced permission checking with detailed permission analysis
   */
  private async checkEnhancedGuildPermissions(
    guildId: string, 
    user: AuthenticatedUser
  ): Promise<GuildPermissionCheck> {
    const userId = user.userId || user.id || user.discordId;
    if (!userId) {
      throw new ForbiddenException('User ID not found in session');
    }

    // OAuth users are pre-validated
    if (user.sessionId === 'discord-session') {
      return {
        guildId,
        userId,
        hasAccess: true,
        permissions: {
          administrator: true,
          manageGuild: true,
          manageChannels: true,
          manageRoles: true,
          manageMessages: true,
          manageWebhooks: true,
          viewAuditLog: true
        }
      };
    }
    
    try {
      // Check bot guild permissions for detailed analysis
      const member = await this.discordUtils.getGuildMember(guildId, userId);
      if (!member) {
        throw new GuildPermissionError(guildId, userId, 'GUILD_ACCESS');
      }

      // Analyze specific permissions
      const permissions = {
        administrator: member.permissions.has('Administrator'),
        manageGuild: member.permissions.has('ManageGuild'),
        manageChannels: member.permissions.has('ManageChannels'),
        manageRoles: member.permissions.has('ManageRoles'),
        manageMessages: member.permissions.has('ManageMessages'),
        manageWebhooks: member.permissions.has('ManageWebhooks'),
        viewAuditLog: member.permissions.has('ViewAuditLog')
      };

      const hasAccess = permissions.administrator || permissions.manageGuild;
      if (!hasAccess) {
        throw new GuildPermissionError(guildId, userId, 'MANAGE_GUILD');
      }

      return {
        guildId,
        userId,
        hasAccess: true,
        permissions
      };
    } catch (error) {
      if (error instanceof GuildPermissionError) {
        throw error;
      }
      this.logger.error(`Enhanced permission check failed for user ${userId} in guild ${guildId}:`, error);
      throw new ForbiddenException('Unable to verify guild permissions');
    }
  }

  /**
   * Legacy permission check for backward compatibility
   */
  private async checkGuildPermissions(guildId: string, user: AuthenticatedUser): Promise<boolean> {
    // OAuth users are already validated by Discord
    if (user.sessionId === 'discord-session') {
      return true;
    }
    
    try {
      const hasPermissions = await this.discordUtils.checkBotGuildPermissions(user.userId || user.id, guildId);
      if (!hasPermissions) {
        throw new ForbiddenException('Insufficient permissions to access this guild');
      }
      return true;
    } catch (error) {
      this.logger.error(`Permission check failed for user ${user.userId || user.id} in guild ${guildId}:`, error);
      throw new ForbiddenException('Unable to verify guild permissions');
    }
  }

  async enableFeature(
    guildId: string, 
    feature: SupportedFeature, 
    user: AuthenticatedUser
  ): Promise<FeatureUpdateResponse> {
    try {
      await this.checkEnhancedGuildPermissions(guildId, user);

      let guild = await this.getTypedGuildFromRedis(guildId);
      const currentConfig = guild?.features?.[feature] || this.getTypedFeatureConfig(feature);
      const updatedConfig = { ...currentConfig, enabled: true };
      
      if (!guild) {
        const discordGuild = await this.discordService.getGuildInfo(guildId) as DiscordGuildInfo;
        const newGuild: GuildCreateType = {
          id: `guild_${Date.now()}`,
          discordId: guildId,
          name: discordGuild?.name || 'Unknown Guild',
          features: { [feature]: updatedConfig } as GuildFeatures,
          isActive: true,
          welcomeEnabled: false,
          starboardEnabled: false,
          starboardThreshold: 3,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        await this.saveGuildToRedis(guildId, newGuild);
        guild = newGuild;
      } else {
        const updatedFeatures: GuildFeatures = {
          ...guild.features,
          [feature]: updatedConfig
        };
        const updatedGuild = { ...guild, features: updatedFeatures, updatedAt: new Date() };
        await this.updateGuildInRedis(guildId, updatedGuild);
        guild = updatedGuild;
      }

      return {
        guildId,
        feature,
        config: guild.features[feature],
        previousConfig: currentConfig,
        message: 'Feature enabled successfully',
        timestamp: new Date()
      };
    } catch (error) {
      this.logger.error(`Failed to enable feature ${feature} for guild ${guildId}:`, error);
      throw error;
    }
  }

  async disableFeature(
    guildId: string, 
    feature: SupportedFeature, 
    user: AuthenticatedUser
  ): Promise<FeatureUpdateResponse> {
    try {
      await this.checkEnhancedGuildPermissions(guildId, user);

      const guild = await this.getTypedGuildFromRedis(guildId);
      const currentConfig = guild?.features?.[feature] || this.getTypedFeatureConfig(feature);
      const updatedConfig = { ...currentConfig, enabled: false };

      if (guild) {
        const updatedFeatures: GuildFeatures = {
          ...guild.features,
          [feature]: updatedConfig
        };
        const updatedGuild = { ...guild, features: updatedFeatures, updatedAt: new Date() };
        await this.updateGuildInRedis(guildId, updatedGuild);
      }

      return {
        guildId,
        feature,
        config: updatedConfig,
        previousConfig: currentConfig,
        message: 'Feature disabled successfully',
        timestamp: new Date()
      };
    } catch (error) {
      this.logger.error(`Failed to disable feature ${feature} for guild ${guildId}:`, error);
      throw error;
    }
  }

  private async getMemberCount(guildId: string): Promise<number> {
    try {
      const guildInfo = await this.discordService.getGuildInfo(guildId);
      return guildInfo.memberCount || 0;
    } catch (error) {
      this.logger.error(`Failed to get member count for guild ${guildId}:`, error);
      return 0;
    }
  }

  // ====== TYPED REDIS HELPER METHODS ======
  
  /**
   * Get guild from Redis with full type safety
   */
  private async getTypedGuildFromRedis(guildId: string): Promise<Guild | null> {
    try {
      const redis = this.redisDatabaseService.redis.getClient();
      const guildKey = `guild:${guildId}`;
      const guildData = await redis.get(guildKey);
      
      if (!guildData) return null;
      
      const parsedGuild = JSON.parse(guildData);
      
      // Ensure date objects are properly converted
      if (parsedGuild.createdAt) parsedGuild.createdAt = new Date(parsedGuild.createdAt);
      if (parsedGuild.updatedAt) parsedGuild.updatedAt = new Date(parsedGuild.updatedAt);
      if (parsedGuild.deletedAt) parsedGuild.deletedAt = new Date(parsedGuild.deletedAt);
      if (parsedGuild.lastActivityAt) parsedGuild.lastActivityAt = new Date(parsedGuild.lastActivityAt);
      
      return parsedGuild as Guild;
    } catch (error) {
      this.logger.error(`Failed to get guild from Redis: ${guildId}`, error);
      return null;
    }
  }

  /**
   * Save guild to Redis with type validation
   */
  private async saveGuildToRedis(guildId: string, guildData: Guild | GuildCreateType): Promise<void> {
    try {
      const redis = this.redisDatabaseService.redis.getClient();
      const guildKey = `guild:${guildId}`;
      
      // Validate guild data structure
      if (!guildData.discordId || !guildData.name) {
        throw new Error('Invalid guild data: missing required fields');
      }
      
      await redis.set(guildKey, JSON.stringify(guildData));
      
      // Set expiration for inactive guilds (optional)
      if (!guildData.isActive) {
        await redis.expire(guildKey, 30 * 24 * 60 * 60); // 30 days
      }
    } catch (error) {
      this.logger.error(`Failed to save guild to Redis: ${guildId}`, error);
      throw error;
    }
  }

  /**
   * Update guild in Redis with type safety
   */
  private async updateGuildInRedis(guildId: string, updates: Partial<Guild>): Promise<void> {
    try {
      const redis = this.redisDatabaseService.redis.getClient();
      const guildKey = `guild:${guildId}`;
      const existingData = await redis.get(guildKey);
      const currentData: Guild = existingData ? JSON.parse(existingData) : {};
      
      // Ensure updatedAt is set
      const updatedData: Guild = { 
        ...currentData, 
        ...updates, 
        updatedAt: new Date() 
      };
      
      // Validate updated data
      if (!updatedData.discordId || !updatedData.name) {
        throw new Error('Invalid guild update: missing required fields');
      }
      
      await redis.set(guildKey, JSON.stringify(updatedData));
    } catch (error) {
      this.logger.error(`Failed to update guild in Redis: ${guildId}`, error);
      throw error;
    }
  }

  /**
   * Legacy getGuildFromRedis for backward compatibility
   */
  private async getGuildFromRedis(guildId: string): Promise<any | null> {
    return this.getTypedGuildFromRedis(guildId);
  }

  // ====== UTILITY METHODS ======

  /**
   * Get comprehensive guild overview with all information
   */
  async getGuildOverview(
    guildId: string, 
    user: AuthenticatedUser
  ): Promise<GuildOverview> {
    const permissionCheck = await this.checkEnhancedGuildPermissions(guildId, user);
    const guildInfo = await this.getGuildInfo(guildId, user);
    const stats = await this.getStats(guildId, user);
    const enabledFeatures = await this.getEnabledFeatures(guildId, user);
    
    // Get feature status for all supported features
    const allFeatures: SupportedFeature[] = [
      'welcome-message', 'music', 'gaming', 'reaction-role', 'meme', 'user-command',
      'leveling', 'moderation', 'economy', 'utility', 'starboard', 'whop',
      'ai-agents', 'role-based-access', 'dev-on-demand', 'content-organization'
    ];
    
    const featureStatuses: FeatureStatus[] = allFeatures.map(feature => ({
      feature,
      enabled: enabledFeatures.features.includes(feature),
      configured: guildInfo.guild.dbFeatures[feature] !== undefined,
      lastModified: guildInfo.guild.lastActivity || undefined
    }));

    return {
      guild: guildInfo.guild,
      features: featureStatuses,
      stats,
      health: {
        botConnected: this.discordService.isReady(),
        permissionsValid: permissionCheck.hasAccess,
        configurationValid: true, // Could implement config validation
        lastHealthCheck: new Date()
      }
    };
  }

  /**
   * Bulk update multiple features
   */
  async bulkUpdateFeatures(
    guildId: string,
    updates: Record<SupportedFeature, FeatureUpdateRequest>,
    user: AuthenticatedUser
  ): Promise<Record<SupportedFeature, FeatureUpdateResponse>> {
    await this.checkEnhancedGuildPermissions(guildId, user);
    
    const results: Record<string, FeatureUpdateResponse> = {};
    
    for (const [feature, updateRequest] of Object.entries(updates)) {
      try {
        results[feature] = await this.updateFeature(
          guildId, 
          feature as SupportedFeature, 
          updateRequest, 
          user
        );
      } catch (error) {
        this.logger.error(`Failed to update feature ${feature}:`, error);
        results[feature] = {
          guildId,
          feature,
          config: null,
          message: `Failed to update: ${error.message}`,
          timestamp: new Date()
        } as any;
      }
    }
    
    return results as Record<SupportedFeature, FeatureUpdateResponse>;
  }

  /**
   * Reset guild configuration to defaults
   */
  async resetGuildConfiguration(
    guildId: string,
    user: AuthenticatedUser
  ): Promise<{ message: string; resetFeatures: string[] }> {
    await this.checkEnhancedGuildPermissions(guildId, user);
    
    const guild = await this.getTypedGuildFromRedis(guildId);
    if (!guild) {
      throw new NotFoundException('Guild not found');
    }
    
    const resetFeatures = Object.keys(guild.features || {});
    
    // Reset to default configuration
    const defaultGuild: Partial<Guild> = {
      features: {} as GuildFeatures,
      settings: {} as GuildSettings,
      welcomeEnabled: false,
      starboardEnabled: false,
      starboardThreshold: 3,
      updatedAt: new Date()
    };
    
    await this.updateGuildInRedis(guildId, defaultGuild);
    
    return {
      message: 'Guild configuration reset to defaults',
      resetFeatures
    };
  }
}