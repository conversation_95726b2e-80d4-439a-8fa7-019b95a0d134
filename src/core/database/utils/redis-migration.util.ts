import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../redis.service';

export interface MigrationScript {
  version: string;
  description: string;
  up: (redisService: RedisService) => Promise<void>;
  down: (redisService: RedisService) => Promise<void>;
}

@Injectable()
export class RedisMigrationUtil {
  private readonly logger = new Logger(RedisMigrationUtil.name);
  
  constructor(private readonly redisService: RedisService) {}

  /**
   * Run all pending migrations
   */
  async migrate(migrations: MigrationScript[]): Promise<void> {
    this.logger.log('Starting Redis migrations...');
    
    const executedMigrations = await this.getExecutedMigrations();
    const pendingMigrations = migrations.filter(
      migration => !executedMigrations.includes(migration.version)
    );

    if (pendingMigrations.length === 0) {
      this.logger.log('No pending migrations');
      return;
    }

    this.logger.log(`Found ${pendingMigrations.length} pending migrations`);

    for (const migration of pendingMigrations) {
      await this.executeMigration(migration);
    }

    this.logger.log('All migrations completed successfully');
  }

  /**
   * Rollback last migration
   */
  async rollback(migrations: MigrationScript[]): Promise<void> {
    const executedMigrations = await this.getExecutedMigrations();
    
    if (executedMigrations.length === 0) {
      this.logger.log('No migrations to rollback');
      return;
    }

    const lastMigration = executedMigrations[executedMigrations.length - 1];
    const migration = migrations.find(m => m.version === lastMigration);

    if (!migration) {
      throw new Error(`Migration ${lastMigration} not found`);
    }

    this.logger.log(`Rolling back migration: ${migration.version} - ${migration.description}`);

    try {
      await migration.down(this.redisService);
      await this.removeMigrationRecord(migration.version);
      this.logger.log(`Rollback completed: ${migration.version}`);
    } catch (error) {
      this.logger.error(`Rollback failed for ${migration.version}:`, error);
      throw error;
    }
  }

  /**
   * Execute a single migration
   */
  private async executeMigration(migration: MigrationScript): Promise<void> {
    this.logger.log(`Executing migration: ${migration.version} - ${migration.description}`);

    try {
      await migration.up(this.redisService);
      await this.recordMigration(migration.version, migration.description);
      this.logger.log(`Migration completed: ${migration.version}`);
    } catch (error) {
      this.logger.error(`Migration failed for ${migration.version}:`, error);
      throw error;
    }
  }

  /**
   * Get list of executed migrations
   */
  private async getExecutedMigrations(): Promise<string[]> {
    const client = this.redisService.getClient();
    return await client.lrange('migrations:executed', 0, -1);
  }

  /**
   * Record migration as executed
   */
  private async recordMigration(version: string, description: string): Promise<void> {
    const client = this.redisService.getClient();
    const migrationData = {
      version,
      description,
      executedAt: new Date().toISOString()
    };

    await client.lpush('migrations:executed', version);
    await client.hset(`migration:${version}`, migrationData);
  }

  /**
   * Remove migration record (for rollback)
   */
  private async removeMigrationRecord(version: string): Promise<void> {
    const client = this.redisService.getClient();
    await client.lrem('migrations:executed', 1, version);
    await client.del(`migration:${version}`);
  }

  /**
   * Get migration history
   */
  async getMigrationHistory(): Promise<Array<{
    version: string;
    description: string;
    executedAt: string;
  }>> {
    const client = this.redisService.getClient();
    const versions = await this.getExecutedMigrations();
    const history = [];

    for (const version of versions) {
      const data = await client.hgetall(`migration:${version}`);
      if (data.version) {
        history.push({
          version: data.version,
          description: data.description || 'No description',
          executedAt: data.executedAt || new Date().toISOString()
        });
      }
    }

    return history.reverse(); // Most recent first
  }
}

/**
 * Utility functions for Redis data management
 */
@Injectable()
export class RedisDataUtil {
  private readonly logger = new Logger(RedisDataUtil.name);

  constructor(private readonly redisService: RedisService) {}

  /**
   * Backup Redis data to JSON
   */
  async backup(): Promise<{ [key: string]: any }> {
    const client = this.redisService.getClient();
    const keys = await client.keys('*');
    const backup: { [key: string]: any } = {};

    this.logger.log(`Backing up ${keys.length} keys...`);

    for (const key of keys) {
      const type = await client.type(key);
      
      switch (type) {
        case 'string':
          backup[key] = await client.get(key);
          break;
        case 'hash':
          backup[key] = await client.hgetall(key);
          break;
        case 'list':
          backup[key] = await client.lrange(key, 0, -1);
          break;
        case 'set':
          backup[key] = await client.smembers(key);
          break;
        case 'zset':
          backup[key] = await client.zrange(key, 0, -1, 'WITHSCORES');
          break;
      }
    }

    this.logger.log('Backup completed');
    return backup;
  }

  /**
   * Restore Redis data from backup
   */
  async restore(backup: { [key: string]: any }, clear = false): Promise<void> {
    const client = this.redisService.getClient();
    
    if (clear) {
      this.logger.log('Clearing existing data...');
      await client.flushall();
    }

    this.logger.log(`Restoring ${Object.keys(backup).length} keys...`);

    for (const [key, value] of Object.entries(backup)) {
      if (Array.isArray(value)) {
        // Could be list, set, or zset with scores
        if (value.length > 0 && typeof value[0] === 'string' && typeof value[1] === 'number') {
          // Assume zset with scores
          await client.zadd(key, ...value);
        } else {
          // Assume list or set - need to determine which
          // For simplicity, restore as list
          await client.lpush(key, ...value);
        }
      } else if (typeof value === 'object' && value !== null) {
        // Hash
        await client.hset(key, value);
      } else {
        // String
        await client.set(key, value);
      }
    }

    this.logger.log('Restore completed');
  }

  /**
   * Clean up expired data
   */
  async cleanup(): Promise<{
    deletedKeys: number;
    processedPatterns: string[];
  }> {
    const client = this.redisService.getClient();
    let deletedKeys = 0;
    const processedPatterns = [];

    // Clean up sessions
    const sessionKeys = await client.keys('sessions:*');
    processedPatterns.push('sessions:*');
    
    for (const key of sessionKeys) {
      const sessionData = await client.hgetall(key);
      if (sessionData.expiresAt) {
        const expiresAt = new Date(sessionData.expiresAt);
        if (expiresAt < new Date()) {
          await client.del(key);
          deletedKeys++;
        }
      }
    }

    // Clean up temporary keys
    const tempKeys = await client.keys('temp:*');
    processedPatterns.push('temp:*');
    
    for (const key of tempKeys) {
      const ttl = await client.ttl(key);
      if (ttl === -1) { // No expiry set
        await client.expire(key, 3600); // Set 1 hour expiry
      }
    }

    this.logger.log(`Cleanup completed: deleted ${deletedKeys} expired keys`);
    
    return {
      deletedKeys,
      processedPatterns
    };
  }

  /**
   * Get Redis statistics
   */
  async getStats(): Promise<{
    totalKeys: number;
    memoryUsage: string;
    keysByType: { [type: string]: number };
    keysByPattern: { [pattern: string]: number };
  }> {
    const client = this.redisService.getClient();
    const info = await client.info('memory');
    const keys = await client.keys('*');
    
    // Count keys by type
    const keysByType: { [type: string]: number } = {};
    for (const key of keys) {
      const type = await client.type(key);
      keysByType[type] = (keysByType[type] || 0) + 1;
    }

    // Count keys by pattern
    const keysByPattern: { [pattern: string]: number } = {};
    const patterns = ['users:*', 'sessions:*', 'guilds:*', 'cache:*', 'temp:*'];
    
    for (const pattern of patterns) {
      const patternKeys = await client.keys(pattern);
      keysByPattern[pattern] = patternKeys.length;
    }

    // Extract memory usage from info
    const memoryMatch = info.match(/used_memory_human:(.+?)\r\n/);
    const memoryUsage = memoryMatch ? memoryMatch[1].trim() : 'unknown';

    return {
      totalKeys: keys.length,
      memoryUsage,
      keysByType,
      keysByPattern
    };
  }

  /**
   * Validate data integrity
   */
  async validateIntegrity(): Promise<{
    valid: boolean;
    issues: Array<{
      key: string;
      issue: string;
      severity: 'low' | 'medium' | 'high';
    }>;
  }> {
    const client = this.redisService.getClient();
    const issues: Array<{
      key: string;
      issue: string;
      severity: 'low' | 'medium' | 'high';
    }> = [];
    
    // Check for orphaned references
    const userKeys = await client.keys('users:*');
    const sessionKeys = await client.keys('sessions:*');
    
    for (const sessionKey of sessionKeys) {
      const sessionData = await client.hgetall(sessionKey);
      if (sessionData.userId) {
        const userExists = await client.exists(`users:${sessionData.userId}`);
        if (!userExists) {
          issues.push({
            key: sessionKey,
            issue: `References non-existent user: ${sessionData.userId}`,
            severity: 'high'
          });
        }
      }
    }

    // Check for malformed data
    for (const userKey of userKeys) {
      const userData = await client.hgetall(userKey);
      if (!userData.discordId) {
        issues.push({
          key: userKey,
          issue: 'Missing required field: discordId',
          severity: 'high'
        });
      }
      
      if (userData.preferences) {
        try {
          JSON.parse(userData.preferences);
        } catch {
          issues.push({
            key: userKey,
            issue: 'Invalid JSON in preferences field',
            severity: 'medium'
          });
        }
      }
    }

    return {
      valid: issues.length === 0,
      issues
    };
  }
}